#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_correct_format():
    """Test với format đúng theo validation model"""
    
    print("=== TEST WITH CORRECT FORMAT ===")
    
    # Test data với format đúng
    test_data = {
        "lesson_id": "234",
        "mon_hoc": "<PERSON><PERSON>a học",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "bai": "Bài 1: Cấu tạo nguyên tử",
                "so_cau": 2,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cấu tạo nguyên tử",
                        "yeu_cau_can_dat": "Hi<PERSON>u được cấu tạo nguyên tử",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",  # Đúng format có dấu
                                "so_cau": 2,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("1. Sending request with correct Vietnamese format...")
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        print(f"2. Status Code: {response.status_code}")
        
        if response.status_code == 200:
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            print(f"3. Total questions: {total_questions}")
            
            if total_questions == '0':
                print("4. PROBLEM: Still 0 questions generated!")
                print("5. Need to check server logs for the actual error")
            else:
                print(f"4. SUCCESS: {total_questions} questions generated")
                
        else:
            print(f"3. ERROR: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"4. Error: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"4. Error text: {response.text}")
                
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    test_correct_format()
