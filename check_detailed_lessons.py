#!/usr/bin/env python3
"""
Check detailed lesson structure
"""
import requests

BASE_URL = "http://localhost:8000/api/v1"

def check_detailed_structure():
    """Check detailed structure of textbooks"""
    try:
        print("=== Checking Detailed Textbook Structure ===")
        
        # Get all textbooks
        response = requests.get(f"{BASE_URL}/pdf/getAllTextBook")
        print(f"Get all textbooks status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            textbooks = data.get('textbooks', [])
            print(f"Found {len(textbooks)} textbooks")
            
            for book in textbooks:
                book_id = book.get('book_id')
                title = book.get('title', 'No title')
                print(f"\n=== Book ID: {book_id} - Title: {title} ===")
                
                if book_id:
                    # Get book structure
                    try:
                        structure_response = requests.get(f"{BASE_URL}/pdf/textbook/{book_id}/structure")
                        print(f"Structure response status: {structure_response.status_code}")
                        
                        if structure_response.status_code == 200:
                            structure = structure_response.json()
                            print(f"Structure keys: {list(structure.keys())}")
                            
                            book_structure = structure.get('book_structure', {})
                            print(f"Book structure keys: {list(book_structure.keys())}")
                            
                            chapters = book_structure.get('chapters', {})
                            print(f"Found {len(chapters)} chapters")
                            
                            for chapter_key, chapter in chapters.items():
                                chapter_title = chapter.get('title', chapter_key)
                                lessons = chapter.get('lessons', [])
                                print(f"  Chapter: {chapter_title} ({len(lessons)} lessons)")
                                
                                for lesson in lessons:
                                    lesson_id = lesson.get('lesson_id')
                                    lesson_title = lesson.get('title', 'No title')
                                    print(f"    Lesson ID: {lesson_id} - {lesson_title}")
                                    
                                    # Test this lesson immediately
                                    if lesson_id:
                                        test_lesson_content(lesson_id)
                                        return lesson_id  # Return first valid lesson
                        else:
                            print(f"Failed to get structure: {structure_response.text[:200]}")
                            
                    except Exception as e:
                        print(f"Error getting structure for {book_id}: {e}")
        
        return None
        
    except Exception as e:
        print(f"Error checking detailed structure: {e}")
        return None

def test_lesson_content(lesson_id):
    """Test getting lesson content"""
    try:
        print(f"\n    Testing lesson content for: {lesson_id}")
        response = requests.get(f"{BASE_URL}/exam/lesson-content/{lesson_id}")
        print(f"    Lesson content status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("    SUCCESS: Lesson content found!")
            content_summary = data.get('content_summary', {})
            print(f"    Content chunks: {content_summary.get('total_chunks', 0)}")
            return True
        else:
            print(f"    FAILED: {response.text[:100]}")
            return False
            
    except Exception as e:
        print(f"    Error testing lesson: {e}")
        return False

def test_exam_generation_with_lesson(lesson_id):
    """Test exam generation with a real lesson"""
    try:
        print(f"\n=== Testing Exam Generation with {lesson_id} ===")
        
        exam_data = {
            "lesson_id": lesson_id,
            "mon_hoc": "Biology",  # ASCII only
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "bai": "Test Lesson",
                    "so_cau": 2,
                    "noi_dung": [
                        {
                            "ten_noi_dung": "Test Content",
                            "yeu_cau_can_dat": "Test Requirements",
                            "muc_do": [
                                {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                            ],
                        }
                    ],
                }
            ],
        }
        
        print("Sending request to /generate-exam...")
        response = requests.post(f"{BASE_URL}/exam/generate-exam", json=exam_data, timeout=120)
        print(f"Exam generation status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("SUCCESS: Exam generated!")
            if 'application/vnd.openxmlformats' in response.headers.get('content-type', ''):
                print(f"Received DOCX file, size: {len(response.content)} bytes")
                
                # Save file
                filename = f"exam_output_{lesson_id}.docx"
                with open(filename, "wb") as f:
                    f.write(response.content)
                print(f"File saved as {filename}")
            return True
        else:
            print(f"FAILED: Status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except:
                print(f"Raw response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"Error testing exam generation: {e}")
        return False

def main():
    print("=== Detailed Lesson Check ===")
    
    # Check detailed structure
    lesson_id = check_detailed_structure()
    
    if lesson_id:
        print(f"\nFound valid lesson: {lesson_id}")
        # Test exam generation
        test_exam_generation_with_lesson(lesson_id)
    else:
        print("\nNo valid lessons found")
    
    print("\n=== Check Complete ===")

if __name__ == "__main__":
    main()
