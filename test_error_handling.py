#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_exam_generation_error():
    """Test exam generation error handling (should return error, not fallback)"""
    
    # Test input data
    request_data = {
        "lesson_id": "234",
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 1,
        "cau_hinh_de": [
            {
                "bai": "Chương 1 - Đ<PERSON>i c<PERSON> hóa học",
                "so_cau": 1,
                "noi_dung": [
                    {
                        "ten_noi_dung": "<PERSON><PERSON>u tạo nguyên tử",
                        "yeu_cau_can_dat": "<PERSON><PERSON><PERSON> c<PERSON>u tạo cơ bản của nguyên tử",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 1,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("Testing exam generation with improved answer key...")
    print("Expected: Should generate DOCX with complete answer key and explanations")
    
    try:
        # Call the API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=request_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("SUCCESS: Got 200 - exam generated")
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            print(f"Total questions: {total_questions}")

            # Save the DOCX file to check answer key
            filename = "test_answer_key_output.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"DOCX file saved as {filename}")
            print(f"File size: {len(response.content)} bytes")
            print("Please check the DOCX file for complete answer key and explanations")

        else:
            print(f"ERROR: Got {response.status_code}")
            print(f"Response: {response.text[:200]}...")

    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_exam_generation_error()
