"""
Test script cho chức năng tạo đề thi từ ma trận
"""

import asyncio
import json
import requests
from typing import Dict, Any

# Test data - Ma trận đề thi mẫu cho Sinh học 12
SAMPLE_EXAM_MATRIX = {
    "lesson_id": "lesson_01_01",  # C<PERSON>n thay đổi theo lesson_id thực tế
    "mon_hoc": "<PERSON>h học",
    "lop": 12,
    "tong_so_cau": 6,
    "cau_hinh_de": [
        {
            "bai": "Bài 1: ADN và Gen",
            "so_cau": 6,
            "noi_dung": [
                {
                    "ten_noi_dung": "<PERSON><PERSON>u trúc phân tử ADN",
                    "yeu_cau_can_dat": "Trình bày được cấu trúc của ADN và nguyên tắc bổ sung",
                    "muc_do": [
                        {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                        {"loai": "Thông hiểu", "so_cau": 2, "loai_cau": ["TN"]},
                        {"loai": "Vận dụng", "so_cau": 2, "loai_cau": ["DT"]},
                    ],
                }
            ],
        }
    ],
}

BASE_URL = "http://localhost:8000/api/v1"


def test_api_connection():
    """Test kết nối API"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"[OK] API Connection: {response.status_code}")
        print(f"Response: {response.json()}")
        return True
    except Exception as e:
        print(f"[ERROR] API Connection failed: {e}")
        return False


def test_get_lesson_content():
    """Test lấy nội dung bài học"""
    try:
        lesson_id = "lesson_01_01"  # Thay đổi theo lesson_id thực tế
        response = requests.get(f"{BASE_URL}/exam/lesson-content/{lesson_id}")

        print(f"\n📖 Testing lesson content retrieval...")
        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Lesson content found")
            print(f"Lesson info: {data.get('lesson_info', {})}")
            print(f"Content summary: {data.get('content_summary', {})}")
            print(
                f"Search quality: {data.get('content_summary', {}).get('search_quality', 0)}"
            )
            return True
        else:
            print(f"❌ Failed to get lesson content: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing lesson content: {e}")
        return False


def test_search_content():
    """Test tìm kiếm nội dung"""
    try:
        search_data = {
            "lesson_id": "lesson_01_01",
            "search_terms": ["ADN", "gen", "cấu trúc"],
            "limit": 5,
        }

        response = requests.post(f"{BASE_URL}/exam/search-content", json=search_data)

        print(f"\n🔍 Testing content search...")
        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search completed")
            print(f"Total found: {data.get('total_found', 0)}")
            print(f"Search quality: {data.get('search_quality', 0)}")
            return True
        else:
            print(f"❌ Search failed: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing search: {e}")
        return False


def test_generate_exam():
    """Test tạo đề thi"""
    try:
        print(f"\n📝 Testing exam generation...")
        print(
            f"Exam matrix: {json.dumps(SAMPLE_EXAM_MATRIX, indent=2, ensure_ascii=False)}"
        )

        response = requests.post(
            f"{BASE_URL}/exam/generate-exam", json=SAMPLE_EXAM_MATRIX
        )

        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Exam generated successfully!")
            print(f"Exam ID: {data.get('exam_id')}")
            print(f"Total questions: {data.get('tong_so_cau')}")
            print(f"DOCX file: {data.get('docx_filename')}")
            print(f"Search quality: {data.get('search_quality')}")

            # In ra một vài câu hỏi mẫu
            questions = data.get("cau_hoi", [])
            if questions:
                print(f"\n📋 Sample questions:")
                for i, q in enumerate(questions[:2], 1):
                    print(f"\nCâu {i} ({q.get('loai_cau')} - {q.get('muc_do')}):")
                    print(f"  {q.get('noi_dung_cau_hoi')}")
                    if q.get("dap_an"):
                        print(f"  Đáp án: {q.get('dap_an')}")

            # In thống kê
            stats = data.get("thong_ke", {})
            if stats:
                print(f"\n📊 Statistics:")
                print(f"  Phân bố theo loại: {stats.get('phan_bo_theo_loai', {})}")
                print(f"  Phân bố theo mức độ: {stats.get('phan_bo_theo_muc_do', {})}")

            return True, data.get("docx_filename")
        else:
            print(f"❌ Exam generation failed: {response.text}")
            return False, None

    except Exception as e:
        print(f"❌ Error testing exam generation: {e}")
        return False, None


def test_download_exam(filename):
    """Test download file đề thi"""
    if not filename:
        print("❌ No filename to download")
        return False

    try:
        print(f"\n💾 Testing file download...")
        response = requests.get(f"{BASE_URL}/exam/download-exam/{filename}")

        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            print(f"✅ File download successful")
            print(f"Content type: {response.headers.get('content-type')}")
            print(f"File size: {len(response.content)} bytes")

            # Lưu file để kiểm tra
            with open(f"downloaded_{filename}", "wb") as f:
                f.write(response.content)
            print(f"File saved as: downloaded_{filename}")
            return True
        else:
            print(f"❌ Download failed: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing download: {e}")
        return False


def test_exam_templates():
    """Test lấy templates"""
    try:
        print(f"\n📋 Testing exam templates...")
        response = requests.get(f"{BASE_URL}/exam/exam-templates")

        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Templates retrieved")
            print(f"Total templates: {data.get('total_templates', 0)}")

            templates = data.get("templates", [])
            for template in templates:
                print(f"  - {template.get('name')}: {template.get('description')}")

            return True
        else:
            print(f"❌ Templates failed: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing templates: {e}")
        return False


def run_all_tests():
    """Chạy tất cả tests"""
    print("Starting Exam Generation API Tests")
    print("=" * 50)

    # Test 1: API Connection
    if not test_api_connection():
        print("❌ API not available. Please start the server first.")
        return

    # Test 2: Exam Templates
    test_exam_templates()

    # Test 3: Lesson Content
    lesson_available = test_get_lesson_content()

    # Test 4: Content Search
    if lesson_available:
        test_search_content()

    # Test 5: Generate Exam
    if lesson_available:
        success, filename = test_generate_exam()

        # Test 6: Download File
        if success and filename:
            test_download_exam(filename)

    print("\n" + "=" * 50)
    print("Tests completed!")
    print("\nNotes:")
    print("- Make sure you have valid lesson_id in your database")
    print("- Check if Qdrant service is running and has data")
    print("- Verify Gemini API key is configured")
    print("- Ensure MongoDB is running")


if __name__ == "__main__":
    run_all_tests()
