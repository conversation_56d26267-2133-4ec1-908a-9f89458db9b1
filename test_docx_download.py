"""
Test DOCX download functionality
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"


def test_docx_download():
    """Test exam generation with DOCX download"""
    try:
        print("=== Testing DOCX Download ===")

        # Test data với Vietnamese diacritics
        exam_data = {
            "lesson_id": "lesson_01_01",
            "mon_hoc": "Sinh học",
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "bai": "Bài 1: ADN và Gen",
                    "so_cau": 2,
                    "noi_dung": [
                        {
                            "ten_noi_dung": "Cấu trúc phân tử ADN",
                            "yeu_cau_can_dat": "Trình bày được cấu trúc của ADN",
                            "muc_do": [
                                {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                            ],
                        }
                    ],
                }
            ],
        }

        print("Request data:")
        print(json.dumps(exam_data, indent=2, ensure_ascii=False))

        # Gửi request
        print("\nSending request...")
        response = requests.post(f"{BASE_URL}/exam/generate-exam", json=exam_data)

        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            # Kiểm tra content type
            content_type = response.headers.get("content-type", "")
            print(f"Content-Type: {content_type}")

            if (
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                in content_type
            ):
                print("[SUCCESS] Received DOCX file!")

                # Lưu file
                filename = response.headers.get("content-disposition", "")
                if "filename=" in filename:
                    filename = filename.split("filename=")[-1].strip('"')
                else:
                    filename = "test_exam.docx"

                with open(filename, "wb") as f:
                    f.write(response.content)

                print(f"[OK] File saved as: {filename}")
                print(f"[OK] File size: {len(response.content)} bytes")

                # Kiểm tra headers bổ sung
                exam_info = response.headers.get("X-Exam-Info", "")
                total_questions = response.headers.get("X-Total-Questions", "")
                search_quality = response.headers.get("X-Search-Quality", "")

                print(f"[INFO] Exam Info: {exam_info}")
                print(f"[INFO] Total Questions: {total_questions}")
                print(f"[INFO] Search Quality: {search_quality}")

                return True
            else:
                print(f"[FAILED] Expected DOCX but got {content_type}")
                print(f"Response content: {response.text[:200]}...")
                return False
        else:
            print(f"[FAILED] Status {response.status_code}")
            try:
                # Try to decode response safely
                response_text = response.text.encode("ascii", "ignore").decode("ascii")
                print(f"Response: {response_text[:300]}...")
            except:
                print("Could not decode response")
            return False

    except Exception as e:
        print(f"[ERROR] {e}")
        return False


def test_api_connection():
    """Test basic API connection"""
    try:
        response = requests.get(f"{BASE_URL}/exam/exam-templates")
        return response.status_code == 200
    except:
        return False


def main():
    print("=== DOCX Download Test ===")

    # Check API connection
    if not test_api_connection():
        print("[ERROR] API not available. Please start the server first.")
        return

    print("[OK] API connection OK")

    # Test DOCX download
    success = test_docx_download()

    if success:
        print("\n[SUCCESS] Test PASSED: DOCX download working correctly!")
    else:
        print("\n[FAILED] Test FAILED: DOCX download not working")

    print("\n=== Test Complete ===")


if __name__ == "__main__":
    main()
