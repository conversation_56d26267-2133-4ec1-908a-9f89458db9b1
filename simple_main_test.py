#!/usr/bin/env python3
"""
Test simple cho main endpoint với ASCII only
"""

import requests

BASE_URL = "http://localhost:8000/api/v1"

def test_main_endpoint():
    """Test main endpoint với ASCII only"""
    
    print("=== Testing Main Endpoint ===")
    
    # Test data ASCII only
    test_data = {
        "lesson_id": "lesson_test_01",
        "mon_hoc": "Hoa_hoc",  # ASCII only
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "bai": "Bai 1",
                "so_cau": 2,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Noi dung 1",
                        "yeu_cau_can_dat": "Yeu cau 1",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 1,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Thông hiểu",
                                "so_cau": 1,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing main endpoint /generate-exam...")
        response = requests.post(
            f"{BASE_URL}/exam/generate-exam",
            json=test_data,
            timeout=60  # Tăng timeout
        )
        
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"[SUCCESS] Main endpoint works!")
            if 'application/vnd.openxmlformats' in response.headers.get('content-type', ''):
                print(f"[OK] Received DOCX file, size: {len(response.content)} bytes")
                
                # Save file để test
                with open("test_main_output.docx", "wb") as f:
                    f.write(response.content)
                print("[OK] File saved as test_main_output.docx")
            else:
                print(f"[WARNING] Unexpected content type: {response.headers.get('content-type')}")
        else:
            print(f"[FAILED] Status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except Exception as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}")
                
    except Exception as e:
        print(f"[ERROR] Exception: {e}")

if __name__ == "__main__":
    test_main_endpoint()
