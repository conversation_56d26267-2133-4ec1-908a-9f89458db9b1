"""
OpenRouter LLM Service để sử dụng Gemini thông qua OpenRouter API
"""
import logging
import requests
import json
import time
from typing import Dict, Any, Optional
from app.core.config import settings

logger = logging.getLogger(__name__)

class OpenRouterLLMService:
    """
    Service sử dụng OpenRouter API để truy cập Gemini models
    """

    def __init__(self):
        self.api_key = None
        self.model_name = "google/gemini-2.0-flash-exp:free"  # Free Gemini model
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self._init_openrouter()

    def _init_openrouter(self):
        """Initialize OpenRouter configuration"""
        try:
            if not settings.GEMINI_API_KEY:
                logger.warning("GEMINI_API_KEY (OpenRouter) not set. LLM features will be disabled.")
                return

            self.api_key = settings.GEMINI_API_KEY
            logger.info(f"OpenRouter API initialized with model: {self.model_name}")

        except Exception as e:
            logger.error(f"Failed to initialize OpenRouter API: {e}")
            self.api_key = None
    
    def is_available(self) -> bool:
        """Check if the service is available"""
        return self.api_key is not None

    def generate_content(self, prompt: str, **kwargs) -> Any:
        """
        Generate content using OpenRouter API (compatible with Gemini API interface)

        Args:
            prompt: The input prompt
            **kwargs: Additional parameters

        Returns:
            Response object with .text attribute
        """
        if not self.api_key:
            raise Exception("OpenRouter API key not initialized")

        # Retry logic for rate limiting
        max_retries = 3
        base_delay = 60  # 60 seconds base delay for rate limits

        for attempt in range(max_retries):
            try:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://planbook-ai.com",  # Your site URL
                    "X-Title": "PlanBook AI",  # Your site name
                }

                data = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": kwargs.get('temperature', settings.GEMINI_TEMPERATURE),
                    "max_tokens": kwargs.get('max_tokens', settings.MAX_TOKENS)
                }

                response = requests.post(
                    url=self.base_url,
                    headers=headers,
                    data=json.dumps(data)
                )

                response.raise_for_status()  # Raise exception for HTTP errors
                response_data = response.json()

                # Create a response object that mimics Gemini API response
                class MockResponse:
                    def __init__(self, content):
                        self.text = content

                return MockResponse(response_data['choices'][0]['message']['content'])

            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:  # Rate limit error
                    if attempt < max_retries - 1:  # Not the last attempt
                        delay = base_delay * (2 ** attempt)  # Exponential backoff
                        logger.warning(f"Rate limit hit (attempt {attempt + 1}/{max_retries}). Waiting {delay} seconds...")
                        time.sleep(delay)
                        continue
                    else:
                        logger.error(f"Rate limit exceeded after {max_retries} attempts")
                        raise Exception(f"OpenRouter rate limit exceeded. Free tier allows 2 requests per minute. Please wait and try again.")
                else:
                    logger.error(f"OpenRouter HTTP error: {e}")
                    raise e
            except Exception as e:
                logger.error(f"OpenRouter API call failed: {e}")
                raise e

    async def format_cv_text(self, raw_text: str) -> Dict[str, Any]:
        """
        Cấu trúc lại text CV thành format đẹp
        
        Args:
            raw_text: Text thô từ PDF
            
        Returns:
            Dict chứa text đã được cấu trúc lại
        """
        try:
            if not self.api_key:
                return {
                    "success": False,
                    "error": "OpenRouter service not available. Please set GEMINI_API_KEY.",
                    "formatted_text": raw_text
                }
            
            prompt = f"""
            Hãy cấu trúc lại text CV sau thành format đẹp và dễ đọc:
            
            {raw_text}
            
            Yêu cầu:
            1. Sắp xếp thông tin theo thứ tự logic
            2. Tách biệt các section rõ ràng
            3. Format đẹp với bullet points
            4. Sửa lỗi chính tả nếu có
            5. Giữ nguyên tất cả thông tin quan trọng
            """
            
            response = self.generate_content(prompt)
            
            return {
                "success": True,
                "formatted_text": response.text,
                "original_length": len(raw_text),
                "formatted_length": len(response.text)
            }
            
        except Exception as e:
            logger.error(f"Error formatting CV text: {e}")
            return {
                "success": False,
                "error": str(e),
                "formatted_text": raw_text
            }

    async def structure_textbook_content(self, content: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Cấu trúc lại nội dung sách giáo khoa
        
        Args:
            content: Nội dung cần cấu trúc
            context: Thông tin context (chapter, lesson, etc.)
            
        Returns:
            Dict chứa nội dung đã được cấu trúc
        """
        try:
            if not self.api_key:
                return {
                    "success": False,
                    "error": "OpenRouter service not available",
                    "structured_content": content
                }
            
            context_info = ""
            if context:
                context_info = f"""
                Context:
                - Chapter: {context.get('chapter_title', 'N/A')}
                - Lesson: {context.get('lesson_title', 'N/A')}
                - Subject: {context.get('subject', 'N/A')}
                """
            
            prompt = f"""
            Hãy cấu trúc lại nội dung sách giáo khoa sau thành format rõ ràng và logic:
            
            {context_info}
            
            Nội dung:
            {content}
            
            Yêu cầu:
            1. Tách biệt các khái niệm chính
            2. Sắp xếp theo thứ tự logic
            3. Highlight các điểm quan trọng
            4. Tạo structure rõ ràng với headers
            5. Giữ nguyên tất cả thông tin khoa học
            """
            
            response = self.generate_content(prompt)
            
            return {
                "success": True,
                "structured_content": response.text,
                "original_length": len(content),
                "structured_length": len(response.text)
            }
            
        except Exception as e:
            logger.error(f"Error structuring textbook content: {e}")
            return {
                "success": False,
                "error": str(e),
                "structured_content": content
            }

# Create global instance
openrouter_llm_service = OpenRouterLLMService()
