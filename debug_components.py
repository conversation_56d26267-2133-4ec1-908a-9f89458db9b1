#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.exam_content_service import exam_content_service
from app.services.llm_service import LLMService
from app.services.exam_generation_service import exam_generation_service
from app.models.exam_models import ExamMatrixRequest, CauHinhDeModel, NoiDungModel, MucDoModel

async def debug_components():
    """Debug từng component để tìm nguyên nhân 0 câu hỏi"""
    
    print("=== DEBUG COMPONENTS ===")
    
    # 1. Test lesson content service
    print("\n1. Testing Lesson Content Service...")
    try:
        lesson_content = await exam_content_service.get_lesson_content_for_exam("234")
        print(f"   Success: {lesson_content.get('success', False)}")
        print(f"   Search quality: {lesson_content.get('search_quality', 0.0)}")
        print(f"   Content keys: {list(lesson_content.keys())}")
        
        if lesson_content.get('success'):
            content_data = lesson_content.get('content', {})
            print(f"   Content data keys: {list(content_data.keys())}")
            main_content = content_data.get('main_content', '')
            print(f"   Main content length: {len(main_content)} characters")
            print(f"   Main content preview: [Content available but skipped due to encoding]")
        else:
            print(f"   ERROR: {lesson_content.get('error', 'Unknown error')}")
            return
            
    except Exception as e:
        print(f"   ERROR: {e}")
        return
    
    # 2. Test LLM service
    print("\n2. Testing LLM Service...")
    try:
        llm_service = LLMService()
        print(f"   Model available: {llm_service.model is not None}")
        print(f"   Service available: {llm_service.is_available()}")
        
        if not llm_service.is_available():
            print("   ERROR: LLM service not available")
            return
            
    except Exception as e:
        print(f"   ERROR: {e}")
        return
    
    # 3. Test simple Gemini API call
    print("\n3. Testing Gemini API...")
    try:
        test_prompt = "Tạo 1 câu hỏi trắc nghiệm về cấu tạo nguyên tử. Trả lời bằng JSON format: [{'cau_hoi': '...', 'dap_an': {'A': '...', 'B': '...', 'C': '...', 'D': '...', 'dung': 'A'}, 'giai_thich': '...'}]"
        
        response = llm_service.model.generate_content(test_prompt)
        response_text = response.text.strip()
        
        print(f"   Response length: {len(response_text)} characters")
        print(f"   Response preview: [Response available but skipped due to encoding]")
        
        # Test parsing
        from app.services.exam_generation_service import ExamGenerationService
        exam_service = ExamGenerationService()
        parsed_questions = exam_service._parse_questions_response(response_text)
        print(f"   Parsed questions count: {len(parsed_questions)}")
        
    except Exception as e:
        print(f"   ERROR: {e}")
        print(f"   Error type: {type(e).__name__}")
        if "429" in str(e) or "quota" in str(e).lower():
            print("   This looks like a quota exceeded error!")
        return
    
    # 4. Test full exam generation
    print("\n4. Testing Full Exam Generation...")
    try:
        # Create test request
        test_request = ExamMatrixRequest(
            lesson_id="234",
            mon_hoc="Hóa học",
            lop=12,
            tong_so_cau=1,  # Chỉ 1 câu để test
            cau_hinh_de=[
                CauHinhDeModel(
                    bai="Bài 1: Cấu tạo nguyên tử",
                    so_cau=1,
                    noi_dung=[
                        NoiDungModel(
                            ten_noi_dung="Cấu tạo nguyên tử",
                            yeu_cau_can_dat="Hiểu được cấu tạo nguyên tử",
                            muc_do=[
                                MucDoModel(
                                    loai="Nhận biết",
                                    so_cau=1,
                                    loai_cau=["TN"]
                                )
                            ]
                        )
                    ]
                )
            ]
        )
        
        result = await exam_generation_service.generate_questions_from_matrix(
            exam_request=test_request,
            lesson_content=lesson_content
        )
        
        print(f"   Success: {result.get('success', False)}")
        print(f"   Total generated: {result.get('total_generated', 0)}")
        print(f"   Questions count: {len(result.get('questions', []))}")
        
        if not result.get('success'):
            print(f"   ERROR: {result.get('error', 'Unknown error')}")
        elif result.get('total_generated', 0) == 0:
            print("   WARNING: 0 questions generated despite success=True")
            
    except Exception as e:
        print(f"   ERROR: {e}")
        print(f"   Error type: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(debug_components())
