"""
Test OpenRouter integration
"""
import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.openrouter_llm_service import openrouter_llm_service
from app.core.config import settings

async def test_openrouter():
    print("=== TEST OPENROUTER INTEGRATION ===")
    
    # Check configuration
    print(f"1. Configuration:")
    print(f"   USE_OPENROUTER: {settings.USE_OPENROUTER}")
    print(f"   OPENROUTER_MODEL: {settings.OPENROUTER_MODEL}")
    print(f"   API Key available: {bool(settings.GEMINI_API_KEY)}")
    print(f"   API Key format: {settings.GEMINI_API_KEY[:10]}..." if settings.GEMINI_API_KEY else "None")
    
    # Check service availability
    print(f"\n2. Service Status:")
    print(f"   Service available: {openrouter_llm_service.is_available()}")
    print(f"   Model name: {openrouter_llm_service.model_name}")
    print(f"   Base URL: {openrouter_llm_service.base_url}")
    
    # Test simple generation
    if openrouter_llm_service.is_available():
        print(f"\n3. Testing content generation...")
        try:
            test_prompt = "Tạo 1 câu hỏi trắc nghiệm về cấu tạo nguyên tử. Trả lời bằng JSON format: [{'cau_hoi': '...', 'dap_an': {'A': '...', 'B': '...', 'C': '...', 'D': '...', 'dung': 'A'}, 'giai_thich': '...'}]"
            
            response = openrouter_llm_service.generate_content(test_prompt)
            print(f"   Response length: {len(response.text)} characters")
            print(f"   Response preview: {response.text[:200]}...")
            print(f"   SUCCESS: OpenRouter working!")
            
        except Exception as e:
            print(f"   ERROR: {e}")
    else:
        print(f"\n3. Service not available - skipping generation test")

if __name__ == "__main__":
    asyncio.run(test_openrouter())
