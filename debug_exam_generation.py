#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def debug_exam_generation():
    """Debug exam generation để tìm nguyên nhân 0 câu hỏi"""
    
    print("=== DEBUG EXAM GENERATION ===")
    
    # Test data đơn giản
    test_data = {
        "lesson_id": "chemistry_grade12_lesson1",
        "mon_hoc": "<PERSON>óa học",
        "lop": 12,
        "tong_so_cau": 5,  # Giảm xuống 5 câu để test
        "cau_hinh_de": [
            {
                "bai": "Bài 1: <PERSON><PERSON>u tạo nguyên tử",
                "so_cau": 5,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cấu tạo nguyên tử",
                        "yeu_cau_can_dat": "Hiểu được cấu tạo nguyên tử, các hạt cơ bản",
                        "muc_do": [
                            {
                                "loai": "<PERSON><PERSON><PERSON><PERSON> biết",
                                "so_cau": 5,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("1. Sending request to generate exam...")
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=120  # Tăng timeout
        )
        
        print(f"2. Status Code: {response.status_code}")
        print(f"3. Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("4. SUCCESS: Got response from API")
            
            # Check headers for debug info
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            test_mode = response.headers.get('x-test-mode', 'false')
            
            print(f"5. Total questions in header: {total_questions}")
            print(f"6. Test mode: {test_mode}")
            print(f"7. Content length: {len(response.content)} bytes")
            
            if len(response.content) > 0:
                # Save file for inspection
                with open("debug_exam.docx", "wb") as f:
                    f.write(response.content)
                print("8. File saved as debug_exam.docx")
            else:
                print("8. ERROR: Empty response content!")
                
        else:
            print(f"4. ERROR: Got {response.status_code}")
            try:
                error_data = response.json()
                print(f"5. Error details: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"5. Error text: {response.text}")
                
    except requests.exceptions.Timeout:
        print("ERROR: Request timeout - API took too long to respond")
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")

if __name__ == "__main__":
    debug_exam_generation()
