"""
Check available lessons in the database
"""
import requests

BASE_URL = "http://localhost:8000/api/v1"

def check_available_lessons():
    """Check what lessons are available"""
    try:
        print("Checking available lessons...")
        
        # Try to get all textbooks
        response = requests.get(f"{BASE_URL}/pdf/getAllTextBook")
        print(f"Get all textbooks status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data.get('textbooks', []))} textbooks")
            
            for book in data.get('textbooks', []):
                print(f"\nBook: {book.get('book_id')} - {book.get('title')}")
                
                # Get book structure
                book_id = book.get('book_id')
                if book_id:
                    structure_response = requests.get(f"{BASE_URL}/pdf/textbook/{book_id}/structure")
                    if structure_response.status_code == 200:
                        structure = structure_response.json()
                        chapters = structure.get('book_structure', {}).get('chapters', {})
                        
                        for chapter_key, chapter in chapters.items():
                            print(f"  Chapter: {chapter.get('title', chapter_key)}")
                            for lesson in chapter.get('lessons', []):
                                lesson_id = lesson.get('lesson_id')
                                lesson_title = lesson.get('title', 'No title')
                                print(f"    Lesson ID: {lesson_id} - {lesson_title}")
                                
                                # Test this lesson
                                if lesson_id:
                                    return lesson_id
        
        return None
        
    except Exception as e:
        print(f"Error checking lessons: {e}")
        return None

def test_lesson_content(lesson_id):
    """Test getting lesson content"""
    try:
        print(f"\nTesting lesson content for: {lesson_id}")
        response = requests.get(f"{BASE_URL}/exam/lesson-content/{lesson_id}")
        print(f"Lesson content status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("SUCCESS: Lesson content found!")
            print(f"Content summary: {data.get('content_summary', {})}")
            return True
        else:
            print(f"FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error testing lesson: {e}")
        return False

def test_exam_with_real_lesson(lesson_id):
    """Test exam generation with real lesson"""
    try:
        print(f"\nTesting exam generation with lesson: {lesson_id}")
        
        exam_data = {
            "lesson_id": lesson_id,
            "mon_hoc": "Biology",
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "bai": "Test Lesson",
                    "so_cau": 2,
                    "noi_dung": [
                        {
                            "ten_noi_dung": "Test Content",
                            "yeu_cau_can_dat": "Test Requirements",
                            "muc_do": [
                                {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                            ],
                        }
                    ],
                }
            ],
        }
        
        response = requests.post(f"{BASE_URL}/exam/generate-exam", json=exam_data)
        print(f"Exam generation status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("SUCCESS: Exam generated!")
            print(f"Exam ID: {data.get('exam_id')}")
            print(f"Total questions: {data.get('tong_so_cau')}")
            print(f"DOCX file: {data.get('docx_filename')}")
            return True
        else:
            # Try to decode response safely
            try:
                response_text = response.text.encode('ascii', 'ignore').decode('ascii')
                print(f"FAILED: {response_text[:200]}...")
            except:
                print(f"FAILED: Status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error testing exam generation: {e}")
        return False

def main():
    print("=== Checking Available Lessons ===")
    
    # Check available lessons
    lesson_id = check_available_lessons()
    
    if lesson_id:
        print(f"\nFound lesson to test: {lesson_id}")
        
        # Test lesson content
        if test_lesson_content(lesson_id):
            # Test exam generation
            test_exam_with_real_lesson(lesson_id)
    else:
        print("\nNo lessons found in database")
        print("You may need to import textbooks first")
    
    print("\n=== Check Complete ===")

if __name__ == "__main__":
    main()
