#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.qdrant_service import qdrant_service

async def check_lessons():
    """Kiểm tra xem có lesson nào trong database"""
    
    print("=== CHECK LESSONS IN DATABASE ===")
    
    try:
        if not qdrant_service.qdrant_client:
            print("ERROR: Qdrant client not available")
            return
            
        # List all collections
        collections = qdrant_service.qdrant_client.get_collections()
        print(f"Available collections: {[c.name for c in collections.collections]}")
        
        # Check each collection for lessons
        for collection in collections.collections:
            collection_name = collection.name
            print(f"\n--- Collection: {collection_name} ---")
            
            try:
                # Get some sample points
                result = qdrant_service.qdrant_client.scroll(
                    collection_name=collection_name,
                    limit=5,
                    with_payload=True
                )
                
                points = result[0]
                print(f"Total points in collection: {len(points)}")
                
                if points:
                    print("Sample lessons:")
                    for i, point in enumerate(points[:3]):
                        payload = point.payload
                        lesson_id = payload.get('lesson_id', 'N/A')
                        title = payload.get('title', payload.get('chapter_title', 'N/A'))
                        try:
                            print(f"  {i+1}. ID: {lesson_id}")
                            if payload:
                                print(f"      Payload keys: {list(payload.keys())}")
                        except UnicodeEncodeError:
                            print(f"  {i+1}. ID: {lesson_id} (encoding issues)")
                            if payload:
                                print(f"      Payload keys: {list(payload.keys())}")
                        
            except Exception as e:
                print(f"Error checking collection {collection_name}: {e}")
                
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    asyncio.run(check_lessons())
