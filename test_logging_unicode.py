#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test logging với Unicode
"""

import sys
import os
import logging

# Fix Windows console encoding
if sys.platform == "win32":
    os.system("chcp 65001")  # Set console to UTF-8
    try:
        sys.stdout.reconfigure(encoding="utf-8")
        sys.stderr.reconfigure(encoding="utf-8")
    except AttributeError:
        # For older Python versions
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.buffer)
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.buffer)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_logging_unicode():
    """Test logging với Vietnamese characters"""
    
    print("=== Testing Logging Unicode ===")
    
    try:
        # Test 1: Log ASCII only
        print("\n--- Test 1: ASCII logging ---")
        logger.info("Starting exam generation for lesson: lesson_test_01")
        logger.info("Subject: Biology")
        print("SUCCESS: ASCII logging works")
        
        # Test 2: Log Vietnamese characters
        print("\n--- Test 2: Vietnamese logging ---")
        lesson_id = "bài_học_test"
        mon_hoc = "Hóa học"
        
        logger.info(f"Starting exam generation for lesson: {lesson_id}")
        logger.info(f"Subject: {mon_hoc}")
        print("SUCCESS: Vietnamese logging works")
        
        # Test 3: Log exception với Vietnamese
        print("\n--- Test 3: Exception logging ---")
        try:
            # Tạo exception có Vietnamese message
            raise ValueError(f"Không tìm thấy bài học: {lesson_id}")
        except Exception as e:
            logger.error(f"Error generating exam: {e}")
            print("SUCCESS: Exception logging works")
        
        # Test 4: String formatting với Vietnamese
        print("\n--- Test 4: String formatting ---")
        filename = f"De_thi_{mon_hoc}_{lesson_id}.docx"
        logger.info(f"Generated filename: {filename}")
        print("SUCCESS: String formatting works")
        
        print("\n=== All logging tests passed! ===")
        
    except Exception as e:
        print(f"ERROR: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_logging_unicode()
