#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test DOCX generation với Unicode
"""

import sys
import os

# Fix Windows console encoding
if sys.platform == "win32":
    os.system("chcp 65001")  # Set console to UTF-8
    try:
        sys.stdout.reconfigure(encoding="utf-8")
        sys.stderr.reconfigure(encoding="utf-8")
    except AttributeError:
        # For older Python versions
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.buffer)
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.buffer)

from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def test_docx_unicode():
    """Test tạo DOCX với Vietnamese characters"""
    
    print("=== Testing DOCX Unicode ===")
    
    try:
        # Test 1: Tạo document với ASCII only
        print("\n--- Test 1: ASCII only ---")
        doc1 = Document()
        
        title = doc1.add_heading("DE KIEM TRA", level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        subtitle = doc1.add_paragraph()
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle.add_run("Mon: Biology - Lop 12")
        
        doc1.save("test_ascii.docx")
        print("SUCCESS: ASCII DOCX created")
        
        # Test 2: Tạo document với Vietnamese characters
        print("\n--- Test 2: Vietnamese characters ---")
        doc2 = Document()
        
        title = doc2.add_heading("ĐỀ KIỂM TRA", level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        subtitle = doc2.add_paragraph()
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle.add_run("Môn: Sinh học - Lớp 12")
        
        doc2.save("test_vietnamese.docx")
        print("SUCCESS: Vietnamese DOCX created")
        
        # Test 3: Test với mon_hoc có dấu
        print("\n--- Test 3: mon_hoc with diacritics ---")
        doc3 = Document()
        
        mon_hoc = "Hóa học"  # Có dấu
        lop = 12
        
        title = doc3.add_heading("ĐỀ KIỂM TRA", level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        subtitle = doc3.add_paragraph()
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle.add_run(f"Môn: {mon_hoc} - Lớp {lop}")
        
        doc3.save("test_mon_hoc.docx")
        print("SUCCESS: mon_hoc DOCX created")
        
        print("\n=== All DOCX tests passed! ===")
        
    except Exception as e:
        print(f"ERROR: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_docx_unicode()
