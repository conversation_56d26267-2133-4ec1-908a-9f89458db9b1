import cv2
import numpy as np


def find_markers_corners(img_bin, min_area=500, max_area=8000):
    # Tìm các contour là hình vuông nhỏ nằm ở các góc phiếu
    contours, _ = cv2.findContours(img_bin, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    candidates = []

    print(f"Found {len(contours)} contours")

    for cnt in contours:
        area = cv2.contourArea(cnt)
        if not (min_area < area < max_area):
            continue
        approx = cv2.approxPolyDP(cnt, 0.04 * cv2.arcLength(cnt, True), True)
        if len(approx) == 4:
            x, y, w, h = cv2.boundingRect(approx)
            aspect = w / h if h != 0 else 0
            if 0.7 < aspect < 1.3:
                cx, cy = x + w // 2, y + h // 2
                candidates.append([cx, cy])
                print(f"Found marker candidate at ({cx}, {cy}) with area {area}")

    print(f"Found {len(candidates)} marker candidates")

    if len(candidates) < 4:
        # Try with more relaxed parameters
        print("Trying with more relaxed parameters...")
        candidates = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if not (100 < area < 20000):  # More relaxed area range
                continue
            approx = cv2.approxPolyDP(cnt, 0.05 * cv2.arcLength(cnt, True), True)
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(approx)
                aspect = w / h if h != 0 else 0
                if 0.5 < aspect < 2.0:  # More relaxed aspect ratio
                    cx, cy = x + w // 2, y + h // 2
                    candidates.append([cx, cy])
                    print(
                        f"Found relaxed marker candidate at ({cx}, {cy}) with area {area}"
                    )

        print(f"Found {len(candidates)} relaxed marker candidates")

        if len(candidates) < 4:
            raise Exception(
                f"Không tìm đủ 4 marker! Chỉ tìm được {len(candidates)} marker. Cần điều chỉnh tham số hoặc kiểm tra ảnh đầu vào."
            )

    candidates = np.array(candidates)
    s = candidates.sum(axis=1)
    diff = np.diff(candidates, axis=1).reshape(-1)
    tl = candidates[np.argmin(s)]
    br = candidates[np.argmax(s)]
    tr = candidates[np.argmin(diff)]
    bl = candidates[np.argmax(diff)]
    return np.array([tl, tr, br, bl], dtype=np.float32)


def align_image_with_corners(image, corners, output_size=(1240, 1754)):
    # output_size phù hợp giấy A4 (tỉ lệ 210x297mm ở 150dpi)
    dst = np.array(
        [
            [0, 0],
            [output_size[0] - 1, 0],
            [output_size[0] - 1, output_size[1] - 1],
            [0, output_size[1] - 1],
        ],
        dtype=np.float32,
    )
    M = cv2.getPerspectiveTransform(corners, dst)
    return cv2.warpPerspective(image, M, output_size)


def extract_bubbles(region, n_col, n_row):
    gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
    bin = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 15, 6
    )
    h, w = bin.shape
    results = []
    cell_w = w // n_col
    cell_h = h // n_row
    for i in range(n_row):
        row_vals = []
        for j in range(n_col):
            x1 = j * cell_w + 2
            x2 = (j + 1) * cell_w - 2
            y1 = i * cell_h + 2
            y2 = (i + 1) * cell_h - 2
            cell = bin[y1:y2, x1:x2]
            # Nếu số pixel trắng (sau nhị phân hóa) lớn hơn ngưỡng thì coi là đã tô
            filled = np.sum(cell > 128) > cell.size * 0.40
            row_vals.append(filled)
        results.append(row_vals)
    return results


def get_digits_from_bubbles(bubbles):
    # bubbles: n_row x n_col, mỗi cột là một chữ số (dọc)
    # Duyệt từng cột, hàng nào True là số
    arr = np.array(bubbles).T
    result = ""
    for col in arr:
        idx = np.where(col)[0]
        if len(idx) == 1:
            result += str(idx[0])
        else:
            result += "?"
    return result


def get_choices_from_bubbles(bubbles, choices="ABCD"):
    # bubbles: n_row x n_col, mỗi hàng là một câu, mỗi cột là đáp án A/B/C/D
    result = []
    for row in bubbles:
        idx = [i for i, v in enumerate(row) if v]
        if len(idx) == 1:
            result.append(choices[idx[0]])
        elif len(idx) > 1:
            result.append("N")  # nhiều đáp án
        else:
            result.append("-")
    return result


def process_omr(file_path):
    print(f"Processing image: {file_path}")

    # 1. Đọc ảnh và căn chỉnh theo 4 marker
    img = cv2.imread(file_path)
    if img is None:
        raise Exception(f"Không thể đọc file ảnh: {file_path}")

    h, w = img.shape[:2]
    print(f"Image dimensions: {w}x{h}")

    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    bin_img = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 21, 12
    )

    try:
        corners = find_markers_corners(bin_img)
        print(f"Found corners: {corners}")
        aligned = align_image_with_corners(img, corners)
    except Exception as e:
        print(f"Error finding markers: {e}")
        print("Sử dụng ảnh gốc thay vì ảnh đã căn chỉnh...")
        aligned = img  # Use original image if alignment fails

    # 2. Cắt các vùng cần thiết (tọa độ đã chuẩn hóa A4, cần tune cho từng form)
    # Student ID (số báo danh)
    try:
        id_region = aligned[72:410, 960:1190]  # 6 số, cột dọc
        bubbles_id = extract_bubbles(id_region, n_col=6, n_row=10)
        student_id = get_digits_from_bubbles(bubbles_id)
    except Exception as e:
        print(f"Error processing student ID: {e}")
        student_id = "ERROR"

    # Test code (mã đề thi)
    try:
        code_region = aligned[72:410, 1190:1360]  # 3 số, cột dọc
        bubbles_code = extract_bubbles(code_region, n_col=3, n_row=10)
        test_code = get_digits_from_bubbles(bubbles_code)
    except Exception as e:
        print(f"Error processing test code: {e}")
        test_code = "ERROR"

    # Câu hỏi trắc nghiệm phần I (40 câu, mỗi khung 10 câu)
    try:
        answer_regions = [
            aligned[430:850, 100:340],  # Câu 1-10
            aligned[430:850, 340:580],  # Câu 11-20
            aligned[430:850, 580:820],  # Câu 21-30
            aligned[430:850, 820:1060],  # Câu 31-40
        ]
        answers = []
        for i, reg in enumerate(answer_regions):
            try:
                # Mỗi block 10 hàng x 4 cột (A,B,C,D)
                ans_bub = extract_bubbles(reg, n_col=4, n_row=10)
                ans = get_choices_from_bubbles(ans_bub)
                answers.extend(ans)
            except Exception as e:
                print(f"Error processing answer region {i+1}: {e}")
                answers.extend(["ERROR"] * 10)
    except Exception as e:
        print(f"Error processing answers: {e}")
        answers = ["ERROR"] * 40

    return {"student_id": student_id, "test_code": test_code, "answers": answers}


# -----------------
if __name__ == "__main__":
    import os

    # Try to find a test image in the project
    test_image_paths = [
        "data/grading/test_images/test_sheet.jpg",
        "data/grading/test_images/fake_answer_sheet.jpg",
        "data/grading/test_images/1.jpeg",
        "data/grading/test_images/2.jpeg",
        "data/grading/1.jpeg",
        "data/grading/2.jpg",
        "data/grading/test_result.jpg",
        "test_student_id_image.jpg",
    ]

    # Find the project root directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(
        os.path.dirname(current_dir)
    )  # Go up 2 levels to project root

    test_image = None
    for img_path in test_image_paths:
        full_path = os.path.join(project_root, img_path)
        if os.path.exists(full_path):
            test_image = full_path
            break

    if test_image:
        try:
            print(f"Processing image: {test_image}")
            result = process_omr(test_image)
            print("Số báo danh:", result["student_id"])
            print("Mã đề thi:", result["test_code"])
            print("Câu trả lời:", result["answers"])
        except Exception as e:
            print(f"Error processing OMR: {e}")
    else:
        print("No test image found. Available test images should be placed in:")
        for img_path in test_image_paths:
            print(f"  - {os.path.join(project_root, img_path)}")
        print("\nTo test this script, please provide a valid image path.")
