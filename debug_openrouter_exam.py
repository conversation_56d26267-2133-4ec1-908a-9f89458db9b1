"""
Debug OpenRouter exam generation
"""
import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.exam_generation_service import ExamGenerationService
from app.services.exam_content_service import exam_content_service
from app.models.exam_models import ExamMatrixRequest, CauHinhDeModel, MucDoModel, NoiDungModel
from app.core.config import settings

async def test_exam_generation():
    print("=== DEBUG OPENROUTER EXAM GENERATION ===")
    
    # Check configuration
    print(f"1. Configuration:")
    print(f"   USE_OPENROUTER: {settings.USE_OPENROUTER}")
    print(f"   OPENROUTER_MODEL: {settings.OPENROUTER_MODEL}")
    
    # Initialize services
    exam_gen_service = ExamGenerationService()
    print(f"\n2. Service Status:")
    print(f"   LLM available: {exam_gen_service._is_llm_available()}")
    
    # Test lesson content
    print(f"\n3. Testing lesson content...")
    lesson_content = await exam_content_service.get_lesson_content_for_exam("234")
    print(f"   Success: {lesson_content.get('success', False)}")
    print(f"   Search quality: {lesson_content.get('search_quality', 0)}")
    
    # Test exam generation
    if lesson_content.get('success') and exam_gen_service._is_llm_available():
        print(f"\n4. Testing exam generation...")
        
        # Create test exam request
        exam_request = ExamMatrixRequest(
            lesson_id="234",
            mon_hoc="Hóa học",
            lop=12,
            tong_so_cau=2,
            cau_hinh_de=[
                CauHinhDeModel(
                    bai="Cấu tạo nguyên tử",
                    so_cau=2,
                    noi_dung=[
                        NoiDungModel(
                            ten_noi_dung="Cấu tạo nguyên tử",
                            yeu_cau_can_dat="Hiểu được cấu tạo nguyên tử",
                            muc_do=[
                                MucDoModel(
                                    loai="Nhận biết",
                                    so_cau=2,
                                    loai_cau=["TN"]
                                )
                            ]
                        )
                    ]
                )
            ]
        )
        
        try:
            result = await exam_gen_service.generate_questions_from_matrix(
                exam_request, lesson_content
            )
            
            print(f"   Success: {result.get('success', False)}")
            print(f"   Total questions: {len(result.get('questions', []))}")
            print(f"   Error: {result.get('error', 'None')}")
            
            if result.get('questions'):
                first_q = result['questions'][0]
                print(f"   First question preview: {first_q.get('cau_hoi', '')[:100]}...")
                print(f"   Answer options: {list(first_q.get('dap_an', {}).keys())}")
                print(f"   Correct answer: {first_q.get('dap_an', {}).get('dung', 'Missing')}")
            
        except Exception as e:
            print(f"   ERROR in generation: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"\n4. Skipping exam generation - prerequisites not met")

if __name__ == "__main__":
    asyncio.run(test_exam_generation())
