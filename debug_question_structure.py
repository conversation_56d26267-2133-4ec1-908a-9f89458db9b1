#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys
import os

# Set UTF-8 encoding for Windows console
if os.name == 'nt':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')

def debug_question_structure():
    """Debug the structure of generated questions"""
    
    # Test data
    test_data = {
        "lesson_id": "test_lesson",
        "mon_hoc": "Hóa học",
        "lop": "12",
        "tong_so_cau": 1,
        "cau_hinh_de": {
            "noi_dung": {
                "Cấu tạo nguyên tử": {
                    "muc_do": {
                        "Nhận biết": {
                            "loai_cau": {
                                "TN": 1
                            }
                        }
                    }
                }
            }
        }
    }
    
    try:
        print("Sending request to generate exam...")
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("SUCCESS: Got DOCX file from API")

            # Save DOCX file
            with open("debug_exam.docx", "wb") as f:
                f.write(response.content)

            print("DOCX file saved as debug_exam.docx")
            print(f"File size: {len(response.content)} bytes")

            # Check headers for question data
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            print(f"Total questions from header: {total_questions}")

            return  # Exit since we got DOCX, not JSON
                    
        else:
            print(f"ERROR: Got {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_question_structure()
