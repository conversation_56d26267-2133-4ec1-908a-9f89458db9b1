"""
Test DOCX generation with mock data
"""
import requests

BASE_URL = "http://localhost:8000/api/v1"

def test_docx_with_mock():
    """Test exam generation with mock data"""
    try:
        print("=== Testing DOCX Generation with Mock Data ===")
        
        # Test data
        exam_data = {
            "lesson_id": "test_lesson_01",
            "mon_hoc": "Biology",
            "lop": 12,
            "tong_so_cau": 4,
            "cau_hinh_de": [
                {
                    "bai": "Test Lesson: DNA and Genes",
                    "so_cau": 4,
                    "noi_dung": [
                        {
                            "ten_noi_dung": "DNA Structure",
                            "yeu_cau_can_dat": "Understand DNA structure and function",
                            "muc_do": [
                                {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                                {"loai": "Thông hiểu", "so_cau": 2, "loai_cau": ["TN"]},
                            ],
                        }
                    ],
                }
            ],
        }
        
        print("Sending request to test endpoint...")
        response = requests.post(f"{BASE_URL}/exam/generate-exam-test", json=exam_data)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            # Check content type
            content_type = response.headers.get('content-type', '')
            print(f"Content-Type: {content_type}")
            
            if 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' in content_type:
                print("[SUCCESS] Received DOCX file!")
                
                # Save file
                filename = response.headers.get('content-disposition', '')
                if 'filename=' in filename:
                    filename = filename.split('filename=')[-1].strip('"')
                else:
                    filename = "test_exam_mock.docx"
                
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"[OK] File saved as: {filename}")
                print(f"[OK] File size: {len(response.content)} bytes")
                
                # Check additional headers
                exam_info = response.headers.get('X-Exam-Info', '')
                total_questions = response.headers.get('X-Total-Questions', '')
                search_quality = response.headers.get('X-Search-Quality', '')
                test_mode = response.headers.get('X-Test-Mode', '')
                
                print(f"[INFO] Exam Info: {exam_info}")
                print(f"[INFO] Total Questions: {total_questions}")
                print(f"[INFO] Search Quality: {search_quality}")
                print(f"[INFO] Test Mode: {test_mode}")
                
                print("\n[SUCCESS] DOCX file generated successfully!")
                print("The file should contain:")
                print("- Exam questions based on the matrix")
                print("- Answer key on a separate page")
                print("- Proper Vietnamese exam formatting")
                
                return True
            else:
                print(f"[FAILED] Expected DOCX but got {content_type}")
                try:
                    response_text = response.text.encode('ascii', 'ignore').decode('ascii')
                    print(f"Response content: {response_text[:200]}...")
                except:
                    print("Could not decode response content")
                return False
        else:
            print(f"[FAILED] Status {response.status_code}")
            try:
                response_text = response.text.encode('ascii', 'ignore').decode('ascii')
                print(f"Response: {response_text[:300]}...")
            except:
                print("Could not decode response")
            return False
            
    except Exception as e:
        print(f"[ERROR] {e}")
        return False

def test_api_connection():
    """Test basic API connection"""
    try:
        response = requests.get(f"{BASE_URL}/exam/exam-templates")
        return response.status_code == 200
    except:
        return False

def main():
    print("=== Mock DOCX Generation Test ===")
    
    # Check API connection
    if not test_api_connection():
        print("[ERROR] API not available. Please start the server first.")
        return
    
    print("[OK] API connection OK")
    
    # Test DOCX generation with mock data
    success = test_docx_with_mock()
    
    if success:
        print("\n[SUCCESS] Test PASSED: DOCX generation working correctly!")
        print("Check the downloaded file to verify:")
        print("1. Exam questions are properly formatted")
        print("2. Answer key is included on separate page")
        print("3. File opens correctly in Word/LibreOffice")
    else:
        print("\n[FAILED] Test FAILED: DOCX generation not working")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
