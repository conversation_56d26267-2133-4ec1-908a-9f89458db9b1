"""
Debug test for exam generation API
"""

import json
import requests

# Simple test data
SAMPLE_EXAM_MATRIX = {
    "lesson_id": "lesson_01_01",
    "mon_hoc": "Biology",
    "lop": 12,
    "tong_so_cau": 2,
    "cau_hinh_de": [
        {
            "bai": "Lesson 1",
            "so_cau": 2,
            "noi_dung": [
                {
                    "ten_noi_dung": "DNA Structure",
                    "yeu_cau_can_dat": "Understand DNA",
                    "muc_do": [
                        {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                    ],
                }
            ],
        }
    ],
}

BASE_URL = "http://localhost:8000/api/v1"


def test_exam_generation():
    """Test exam generation"""
    try:
        print("Testing exam generation...")
        print("Request data:")
        print(json.dumps(SAMPLE_EXAM_MATRIX, indent=2))

        response = requests.post(
            f"{BASE_URL}/exam/generate-exam", json=SAMPLE_EXAM_MATRIX
        )
        print(f"\nResponse status: {response.status_code}")

        # Try to get response text without printing Vietnamese characters
        try:
            response_text = response.text
            # Replace problematic characters
            response_text = response_text.encode("ascii", "ignore").decode("ascii")
            print(f"Response (ASCII only): {response_text[:500]}...")
        except:
            print("Could not decode response text")

        if response.status_code == 200:
            print("SUCCESS!")
        else:
            print("FAILED!")

    except Exception as e:
        print(f"Error: {e}")


def main():
    print("=== Debug Test ===")
    test_exam_generation()


if __name__ == "__main__":
    main()
