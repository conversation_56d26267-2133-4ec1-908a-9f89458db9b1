"""
Test API với debug chi tiết
"""
import requests
import json

def test_api_debug():
    """Test API với debug chi tiết"""
    
    print("=== TEST API DEBUG ===")
    
    # Test data
    test_data = {
        "lesson_id": "234",
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "bai": "Cấu tạo nguyên tử",
                "so_cau": 2,
                "noi_dung": [
                    {
                        "ten_noi_dung": "<PERSON><PERSON>u tạo nguyên tử",
                        "yeu_cau_can_dat": "<PERSON><PERSON><PERSON> được cấu tạo nguyên tử",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 2,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("1. Sending request...")
        print(f"   Data: {json.dumps(test_data, indent=2, ensure_ascii=True)}")
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        print(f"\n2. Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            test_mode = response.headers.get('x-test-mode', 'false')
            search_quality = response.headers.get('x-search-quality', 'Unknown')
            
            print(f"   Total questions: {total_questions}")
            print(f"   Test mode: {test_mode}")
            print(f"   Search quality: {search_quality}")
            
            # Check if it's a file response
            content_type = response.headers.get('content-type', '')
            if 'application/vnd.openxmlformats-officedocument' in content_type:
                print(f"   Response type: DOCX file ({len(response.content)} bytes)")
                
                if total_questions == '0':
                    print("   PROBLEM: 0 questions but DOCX created")
                else:
                    print(f"   SUCCESS: {total_questions} questions in DOCX")
            else:
                print(f"   Response type: {content_type}")
                print(f"   Content preview: {response.text[:200]}...")
                
        else:
            print(f"   ERROR: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {json.dumps(error_data, indent=2, ensure_ascii=True)}")
            except:
                print(f"   Error text: {response.text}")

    except Exception as e:
        print(f"EXCEPTION: {e}")

if __name__ == "__main__":
    test_api_debug()
