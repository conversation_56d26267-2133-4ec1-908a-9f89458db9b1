"""
Debug OpenRouter trực tiếp
"""
import asyncio
import sys
import os
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.openrouter_llm_service import openrouter_llm_service
from app.core.config import settings

async def test_openrouter_direct():
    print("=== DEBUG OPENROUTER DIRECT ===")
    
    # Test prompt giống như trong exam generation
    test_prompt = """
Bạn là một chuyên gia giáo dục, hãy tạo câu hỏi trắc nghiệm về chủ đề "Cấu tạo nguyên tử" ở mức độ "Nhận biết".

Yêu cầu:
- Tạo đúng 2 câu hỏi trắc nghiệm
- Mỗi câu có 4 đáp án A, B, C, D
- Chỉ có 1 đáp án đúng
- Có giải thích chi tiết

Trả về kết quả dưới dạng JSON array với format:
[
  {
    "cau_hoi": "Câu hỏi...",
    "dap_an": {
      "A": "Đáp án A",
      "B": "Đáp án B", 
      "C": "Đáp án C",
      "D": "Đáp án D",
      "dung": "A"
    },
    "giai_thich": "Giải thích..."
  }
]

Chỉ trả về JSON, không thêm text khác.
"""
    
    try:
        print("1. Testing OpenRouter service availability...")
        print(f"   Available: {openrouter_llm_service.is_available()}")
        print(f"   Model: {openrouter_llm_service.model_name}")
        print(f"   API Key: {settings.GEMINI_API_KEY[:10]}..." if settings.GEMINI_API_KEY else "None")
        
        if not openrouter_llm_service.is_available():
            print("   ERROR: Service not available")
            return
            
        print("\n2. Sending request to OpenRouter...")
        response = openrouter_llm_service.generate_content(test_prompt)
        
        print(f"   Response type: {type(response)}")
        print(f"   Response length: {len(response.text)} characters")
        print(f"   Response preview: {response.text[:200].encode('ascii', 'ignore').decode('ascii')}...")
        
        print("\n3. Attempting to parse JSON...")
        try:
            # Try to parse as JSON
            response_text = response.text.strip()
            
            # Remove markdown code blocks if present
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            print(f"   Cleaned text: {response_text[:200].encode('ascii', 'ignore').decode('ascii')}...")
            
            questions_data = json.loads(response_text)
            print(f"   SUCCESS: Parsed {len(questions_data)} questions")
            
            for i, q in enumerate(questions_data):
                print(f"   Question {i+1}:")
                print(f"     - Question: {q.get('cau_hoi', 'Missing')[:50].encode('ascii', 'ignore').decode('ascii')}...")
                print(f"     - Answers: {list(q.get('dap_an', {}).keys())}")
                print(f"     - Correct: {q.get('dap_an', {}).get('dung', 'Missing')}")
                print(f"     - Explanation: {q.get('giai_thich', 'Missing')[:50].encode('ascii', 'ignore').decode('ascii')}...")
                
        except json.JSONDecodeError as e:
            print(f"   JSON PARSE ERROR: {e}")
            print(f"   Raw response: {response.text}")
            
        except Exception as e:
            print(f"   OTHER ERROR: {e}")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_openrouter_direct())
