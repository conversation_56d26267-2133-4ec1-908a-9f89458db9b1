#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def debug_with_logs():
    """Debug với logs chi tiết"""
    
    print("=== DEBUG WITH DETAILED LOGS ===")
    
    # Test data đơn giản hơn
    test_data = {
        "lesson_id": "chemistry_grade12_lesson1",
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 2,  # Chỉ 2 câu để test
        "cau_hinh_de": [
            {
                "bai": "Bài 1: Cấu tạo nguyên tử",
                "so_cau": 2,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cấu tạo nguyên tử",
                        "yeu_cau_can_dat": "Hi<PERSON>u được cấu tạo nguyên tử",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 2,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("1. Sending request...")
        print(f"Request data: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=180
        )
        end_time = time.time()
        
        print(f"2. Request completed in {end_time - start_time:.2f} seconds")
        print(f"3. Status Code: {response.status_code}")
        
        if response.status_code == 200:
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            print(f"4. Total questions: {total_questions}")
            
            if total_questions == '0':
                print("5. ERROR: 0 questions generated!")
                print("6. This indicates an issue in the question generation process")
                print("7. Check server logs for detailed error information")
            else:
                print(f"5. SUCCESS: {total_questions} questions generated")
                
        else:
            print(f"4. ERROR: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"5. Error: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"5. Error text: {response.text}")
                
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    debug_with_logs()
