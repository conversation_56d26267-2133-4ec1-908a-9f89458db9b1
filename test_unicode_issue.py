#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Unicode issue với mock data để tìm root cause
"""

import requests
import json
import sys
import os

# Fix Windows console encoding
if sys.platform == "win32":
    os.system("chcp 65001")  # Set console to UTF-8
    try:
        sys.stdout.reconfigure(encoding="utf-8")
        sys.stderr.reconfigure(encoding="utf-8")
    except AttributeError:
        # For older Python versions
        import codecs

        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.buffer)
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.buffer)

BASE_URL = "http://localhost:8000/api/v1"


def test_unicode_step_by_step():
    """Test từng bước để tìm Unicode issue"""

    print("=== Testing Unicode Issue Step by Step ===")

    # Test 1: ASCII only data
    print("\n--- Test 1: ASCII Only ---")
    ascii_data = {
        "lesson_id": "test_lesson_ascii",
        "mon_hoc": "Biology",  # ASCII only
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "bai": "Test Lesson",
                "so_cau": 2,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Test Content",
                        "yeu_cau_can_dat": "Test Requirements",
                        "muc_do": [
                            {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                        ],
                    }
                ],
            }
        ],
    }

    test_request(ascii_data, "ASCII only")

    # Test 2: Vietnamese characters in lesson_id only
    print("\n--- Test 2: Vietnamese in lesson_id ---")
    vietnamese_lesson_data = ascii_data.copy()
    vietnamese_lesson_data["lesson_id"] = "bài_học_test"

    test_request(vietnamese_lesson_data, "Vietnamese lesson_id")

    # Test 3: Vietnamese characters in mon_hoc only
    print("\n--- Test 3: Vietnamese in mon_hoc ---")
    vietnamese_subject_data = ascii_data.copy()
    vietnamese_subject_data["mon_hoc"] = "Sinh học"

    test_request(vietnamese_subject_data, "Vietnamese mon_hoc")

    # Test 4: Vietnamese in content fields
    print("\n--- Test 4: Vietnamese in content ---")
    vietnamese_content_data = ascii_data.copy()
    vietnamese_content_data["cau_hinh_de"][0]["bai"] = "Bài học test"
    vietnamese_content_data["cau_hinh_de"][0]["noi_dung"][0]["ten_noi_dung"] = (
        "Nội dung test"
    )
    vietnamese_content_data["cau_hinh_de"][0]["noi_dung"][0]["yeu_cau_can_dat"] = (
        "Yêu cầu test"
    )

    test_request(vietnamese_content_data, "Vietnamese content")


def test_request(data, test_name):
    """Test một request và log kết quả"""
    try:
        print(f"Testing: {test_name}")
        print(f"Data: {json.dumps(data, ensure_ascii=False)[:100]}...")

        # Test với test endpoint trước
        response = requests.post(
            f"{BASE_URL}/exam/generate-exam-test", json=data, timeout=60
        )

        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            print(f"[SUCCESS] {test_name} works!")
            content_type = response.headers.get("content-type", "")
            if "application/vnd.openxmlformats" in content_type:
                print(f"Received DOCX file, size: {len(response.content)} bytes")
            else:
                print(f"Unexpected content type: {content_type}")
        else:
            print(f"[FAILED] Status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except Exception as e:
                print(f"JSON decode error: {e}")
                # Try to print raw response safely
                try:
                    raw_text = response.text.encode("ascii", "ignore").decode("ascii")
                    print(f"Raw response: {raw_text[:200]}")
                except:
                    print(f"Cannot decode response text")

    except Exception as e:
        print(f"[ERROR] Exception: {e}")

    print("-" * 50)


if __name__ == "__main__":
    test_unicode_step_by_step()
