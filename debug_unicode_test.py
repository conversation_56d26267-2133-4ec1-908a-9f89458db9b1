#!/usr/bin/env python3
"""
Debug script để test Unicode encoding trong exam generation
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"


def test_unicode_encoding():
    """Test với các ký tự tiếng Việt khác nhau"""

    print("=== Unicode Encoding Debug Test ===")

    # Test data với format đúng theo ExamMatrixRequest model
    test_cases = [
        {
            "name": "ASCII only",
            "data": {
                "lesson_id": "lesson_test_01",
                "mon_hoc": "Hoa_hoc",  # Không dấu
                "lop": 12,
                "tong_so_cau": 2,
                "cau_hinh_de": [
                    {
                        "bai": "Bai 1",
                        "so_cau": 2,
                        "noi_dung": [
                            {
                                "ten_noi_dung": "Noi dung 1",
                                "yeu_cau_can_dat": "Yeu cau 1",
                                "muc_do": [
                                    {
                                        "loai": "<PERSON><PERSON><PERSON><PERSON> biết",
                                        "so_cau": 1,
                                        "loai_cau": ["TN"],
                                    },
                                    {
                                        "loai": "Thông hiểu",
                                        "so_cau": 1,
                                        "loai_cau": ["TN"],
                                    },
                                ],
                            }
                        ],
                    }
                ],
            },
        },
        {
            "name": "Vietnamese with diacritics",
            "data": {
                "lesson_id": "bài_học_01",  # Có dấu
                "mon_hoc": "Hóa học",  # Có dấu
                "lop": 12,
                "tong_so_cau": 2,
                "cau_hinh_de": [
                    {
                        "bai": "Bài 1",
                        "so_cau": 2,
                        "noi_dung": [
                            {
                                "ten_noi_dung": "Nội dung 1",
                                "yeu_cau_can_dat": "Yêu cầu 1",
                                "muc_do": [
                                    {
                                        "loai": "Nhận biết",
                                        "so_cau": 1,
                                        "loai_cau": ["TN"],
                                    },
                                    {
                                        "loai": "Thông hiểu",
                                        "so_cau": 1,
                                        "loai_cau": ["TN"],
                                    },
                                ],
                            }
                        ],
                    }
                ],
            },
        },
    ]

    for test_case in test_cases:
        print(f"\n--- Testing: {test_case['name']} ---")

        try:
            # Test với endpoint test trước
            print("Testing with /generate-exam-test...")
            response = requests.post(
                f"{BASE_URL}/exam/generate-exam-test",
                json=test_case["data"],
                timeout=30,
            )

            print(f"Status: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")

            if response.status_code == 200:
                print(f"[SUCCESS] Test endpoint works for {test_case['name']}")
                if "application/vnd.openxmlformats" in response.headers.get(
                    "content-type", ""
                ):
                    print(
                        f"[OK] Received DOCX file, size: {len(response.content)} bytes"
                    )
                else:
                    print(
                        f"[WARNING] Unexpected content type: {response.headers.get('content-type')}"
                    )
            else:
                print(f"[FAILED] Status {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data}")
                except Exception as e:
                    print(f"JSON decode error: {e}")
                    print(f"Raw response: {response.text[:500]}")

        except Exception as e:
            print(f"[ERROR] Exception: {e}")

        print("-" * 50)


if __name__ == "__main__":
    test_unicode_encoding()
