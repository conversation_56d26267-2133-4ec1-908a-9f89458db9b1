#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from docx import Document
import sys
import os

# Set UTF-8 encoding for Windows console
if os.name == 'nt':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')

def check_docx_content(filename):
    """Check DOCX content for answer key and explanations"""

    try:
        doc = Document(filename)
        print(f"Checking DOCX file: {filename}")
        print("=" * 50)
        
        found_answer_key = False
        found_explanations = False
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            # Check for answer key section
            if "DAP AN VA HUONG DAN CHAM" in text or "ĐÁP ÁN" in text:
                found_answer_key = True
                print(f"Found answer key section at paragraph {i}")

            # Check for explanations
            if "Giai thich" in text or "Giải thích" in text:
                found_explanations = True
                print(f"Found explanation at paragraph {i}: {text[:100]}")

            # Print important sections
            if any(keyword in text for keyword in ["D<PERSON> AN", "ĐÁP ÁN", "Giai thich", "Giải thích", "Cau", "Câu"]):
                print(f"Paragraph {i}: {text[:150]}")
        
        # Check tables for answer key
        print(f"\nTables found: {len(doc.tables)}")
        for i, table in enumerate(doc.tables):
            print(f"Table {i} has {len(table.rows)} rows and {len(table.columns)} columns")
            
            # Print first few rows of each table
            for row_idx, row in enumerate(table.rows[:3]):  # First 3 rows
                row_text = " | ".join([cell.text.strip() for cell in row.cells])
                print(f"  Row {row_idx}: {row_text}")
        
        print("\n" + "=" * 50)
        print("SUMMARY:")
        print(f"Answer key section found: {found_answer_key}")
        print(f"Explanations found: {found_explanations}")
        print(f"Total paragraphs: {len(doc.paragraphs)}")
        print(f"Total tables: {len(doc.tables)}")

        if found_answer_key and found_explanations:
            print("SUCCESS: DOCX contains both answer key and explanations!")
        else:
            print("ISSUE: Missing answer key or explanations")
            
    except Exception as e:
        print(f"Error reading DOCX: {e}")

if __name__ == "__main__":
    filename = "test_answer_key_output.docx"
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    
    check_docx_content(filename)
