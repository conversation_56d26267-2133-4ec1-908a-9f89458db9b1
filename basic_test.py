"""
Basic test for exam generation API
"""
import json
import requests

# Simple test data without Vietnamese characters
SAMPLE_EXAM_MATRIX = {
    "lesson_id": "lesson_01_01",
    "mon_hoc": "Biology",
    "lop": 12,
    "tong_so_cau": 4,
    "cau_hinh_de": [
        {
            "bai": "Lesson 1: DNA and Genes",
            "so_cau": 4,
            "noi_dung": [
                {
                    "ten_noi_dung": "DNA Structure",
                    "yeu_cau_can_dat": "Understand DNA structure",
                    "muc_do": [
                        {"loai": "Nhan biet", "so_cau": 2, "loai_cau": ["TN"]},
                        {"loai": "Thong hieu", "so_cau": 2, "loai_cau": ["TN"]},
                    ],
                }
            ],
        }
    ],
}

BASE_URL = "http://localhost:8000/api/v1"

def test_connection():
    """Test connection"""
    try:
        response = requests.get("http://localhost:8000/")
        print(f"Root API Status: {response.status_code}")
        if response.status_code == 200:
            print("API is running!")
            return True
        return False
    except Exception as e:
        print(f"Connection failed: {e}")
        return False

def test_exam_generation():
    """Test exam generation"""
    try:
        print("\nTesting exam generation...")
        
        response = requests.post(f"{BASE_URL}/exam/generate-exam", json=SAMPLE_EXAM_MATRIX)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("SUCCESS: Exam generated!")
            print(f"Exam ID: {data.get('exam_id')}")
            print(f"Total questions: {data.get('tong_so_cau')}")
            print(f"DOCX file: {data.get('docx_filename')}")
            
            # Print sample questions
            questions = data.get('cau_hoi', [])
            if questions:
                print("\nSample questions:")
                for i, q in enumerate(questions[:2], 1):
                    print(f"\nQuestion {i}:")
                    print(f"  Type: {q.get('loai_cau')} - Level: {q.get('muc_do')}")
                    print(f"  Content: {q.get('noi_dung_cau_hoi')}")
                    print(f"  Answer: {q.get('dap_an')}")
            
            return True
        else:
            print(f"FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_templates():
    """Test exam templates"""
    try:
        print("\nTesting exam templates...")
        response = requests.get(f"{BASE_URL}/exam/exam-templates")
        print(f"Templates status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Found {data.get('total_templates', 0)} templates")
            return True
        else:
            print(f"Templates failed: {response.text}")
            return False
    except Exception as e:
        print(f"Templates error: {e}")
        return False

def main():
    print("=== Basic Exam Generation Test ===")
    
    # Test connection
    if not test_connection():
        return
    
    # Test templates
    test_templates()
    
    # Test exam generation
    test_exam_generation()
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
