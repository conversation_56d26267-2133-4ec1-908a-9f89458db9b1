"""
Test script để debug việc tạo đề thi
"""

import asyncio
import json
import logging
import requests
from typing import Dict, Any

# Cấu hình logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

BASE_URL = "http://localhost:8000/api/v1"

# Test data - Ma trận đề thi đơn giản
SIMPLE_EXAM_MATRIX = {
    "lesson_id": "lesson_01_01",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 2,
    "cau_hinh_de": [
        {
            "bai": "Bài 1: <PERSON><PERSON>u trúc nguyên tử",
            "so_cau": 2,
            "noi_dung": [
                {
                    "ten_noi_dung": "Cấu trúc nguyên tử",
                    "yeu_cau_can_dat": "<PERSON><PERSON><PERSON> được cấu trúc c<PERSON> bản của nguyên tử",
                    "muc_do": [
                        {
                            "loai": "<PERSON>hận biết",
                            "so_cau": 2,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

def test_exam_generation():
    """Test tạo đề thi với logging chi tiết"""
    try:
        print("🧪 Testing exam generation with detailed logging...")
        print(f"Request data: {json.dumps(SIMPLE_EXAM_MATRIX, indent=2, ensure_ascii=False)}")
        
        # Test endpoint generate-exam-test (sử dụng mock data)
        print("\n📝 Testing /exam/generate-exam-test endpoint...")
        response = requests.post(
            f"{BASE_URL}/exam/generate-exam-test",
            json=SIMPLE_EXAM_MATRIX,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Success! DOCX file generated")
            # Lưu file để kiểm tra
            with open("test_exam_output.docx", "wb") as f:
                f.write(response.content)
            print("📄 File saved as test_exam_output.docx")
        else:
            print(f"❌ Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"Error text: {response.text}")
                
    except Exception as e:
        print(f"❌ Exception occurred: {e}")

def test_llm_service():
    """Test LLM service trực tiếp"""
    try:
        print("\n🤖 Testing LLM service directly...")
        
        # Import và test LLM service
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.llm_service import llm_service
        
        print(f"LLM service available: {llm_service.is_available()}")
        print(f"Model: {llm_service.model}")
        
        if llm_service.model:
            print("✅ LLM service is working")
            
            # Test simple generation
            test_prompt = "Tạo 1 câu hỏi trắc nghiệm về cấu trúc nguyên tử. Trả về JSON format: [{'cau_hoi': 'câu hỏi', 'dap_an': {'A': 'đáp án A', 'B': 'đáp án B', 'C': 'đáp án C', 'D': 'đáp án D', 'dung': 'A'}}]"
            
            print(f"\nTesting with prompt: {test_prompt}")
            response = llm_service.model.generate_content(test_prompt)
            print(f"Response: {response.text}")
        else:
            print("❌ LLM service not available")
            
    except Exception as e:
        print(f"❌ Exception in LLM test: {e}")

def test_exam_generation_service():
    """Test ExamGenerationService trực tiếp"""
    try:
        print("\n⚙️ Testing ExamGenerationService directly...")
        
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.exam_generation_service import exam_generation_service
        from app.models.exam_models import ExamMatrixRequest
        
        # Tạo request object
        exam_request = ExamMatrixRequest(**SIMPLE_EXAM_MATRIX)
        
        # Mock lesson content
        mock_lesson_content = {
            "success": True,
            "content": {
                "main_content": "Nguyên tử là đơn vị cơ bản của vật chất. Nguyên tử gồm hạt nhân mang điện tích dương và các electron mang điện tích âm quay quanh hạt nhân.",
                "lesson_info": {
                    "chapter_title": "Chương 1: Cấu trúc nguyên tử",
                    "lesson_title": "Bài 1: Cấu trúc nguyên tử"
                }
            }
        }
        
        print("Calling generate_questions_from_matrix...")
        
        # Gọi service
        result = asyncio.run(
            exam_generation_service.generate_questions_from_matrix(
                exam_request, mock_lesson_content
            )
        )
        
        print(f"Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"❌ Exception in service test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting exam generation debug tests...")
    
    # Test 1: LLM Service
    test_llm_service()
    
    # Test 2: ExamGenerationService
    test_exam_generation_service()
    
    # Test 3: API Endpoint
    test_exam_generation()
    
    print("\n✅ All tests completed!")
