#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_improved_exam():
    """Test các cải tiến: bảng đáp án gọn hơn, g<PERSON><PERSON><PERSON> thí<PERSON> sâu h<PERSON>n, auto cleanup file"""
    
    print("Testing improved exam generation...")
    
    # Test data với 10 câu hỏi để test bảng đáp án
    test_data = {
        "lesson_id": "chemistry_grade12_lesson1",
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 20,
        "cau_hinh_de": [
            {
                "bai": "Bài 1: <PERSON><PERSON><PERSON> tạo nguyên tử",
                "so_cau": 20,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cấu tạo nguyên tử",
                        "yeu_cau_can_dat": "Hiểu được cấu tạo nguyên tử, cá<PERSON> hạt c<PERSON> bả<PERSON>",
                        "muc_do": [
                            {
                                "loai": "<PERSON><PERSON><PERSON><PERSON> biết",
                                "so_cau": 20,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Sending request to generate improved exam...")
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: Got DOCX file from API")
            
            # Save DOCX file
            with open("test_improved_exam.docx", "wb") as f:
                f.write(response.content)
            
            print("DOCX file saved as test_improved_exam.docx")
            print(f"File size: {len(response.content)} bytes")
            
            # Check headers
            total_questions = response.headers.get('x-total-questions', 'Unknown')
            print(f"Total questions: {total_questions}")
            
            print("\n[OK] Improvements to check:")
            print("1. Bang dap an: Nen co 1 hang voi 20 cot (hoac chia ro rang)")
            print("2. Giai thich: Nen sau hon voi vi du minh hoa")
            print("3. File cleanup: File se tu dong xoa khoi server sau download")
            
        else:
            print(f"ERROR: Got {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Error text: {response.text}")
                
    except Exception as e:
        print(f"Exception occurred: {e}")

if __name__ == "__main__":
    test_improved_exam()
