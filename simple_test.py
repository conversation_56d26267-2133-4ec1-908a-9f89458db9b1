"""
Simple test script cho chức năng tạo đề thi
"""

import json
import requests

# Test data
SAMPLE_EXAM_MATRIX = {
    "lesson_id": "lesson_01_01",
    "mon_hoc": "<PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 4,
    "cau_hinh_de": [
        {
            "bai": "Bài 1: ADN và Gen",
            "so_cau": 4,
            "noi_dung": [
                {
                    "ten_noi_dung": "Cấu trúc phân tử ADN",
                    "yeu_cau_can_dat": "Trình bày được cấu trúc của ADN",
                    "muc_do": [
                        {"loai": "Nhận biết", "so_cau": 2, "loai_cau": ["TN"]},
                        {"loai": "Thông hiểu", "so_cau": 2, "loai_cau": ["TN"]},
                    ],
                }
            ],
        }
    ],
}

BASE_URL = "http://localhost:8000/api/v1"


def test_connection():
    """Test kết nối"""
    try:
        # Test root endpoint
        response = requests.get("http://localhost:8000/")
        print(f"Root API Status: {response.status_code}")
        if response.status_code == 200:
            print("API is running!")
            print(f"Response: {response.json()}")
            return True

        # Test API prefix endpoint
        response2 = requests.get(f"{BASE_URL}/")
        print(f"API Prefix Status: {response2.status_code}")
        return response2.status_code == 200

    except Exception as e:
        print(f"Connection failed: {e}")
        print("Please start the server with: fastapi dev app/main.py")
        return False


def test_exam_generation():
    """Test tạo đề thi"""
    try:
        print("\nTesting exam generation...")
        print(
            f"Request data: {json.dumps(SAMPLE_EXAM_MATRIX, indent=2, ensure_ascii=False)}"
        )

        response = requests.post(
            f"{BASE_URL}/exam/generate-exam", json=SAMPLE_EXAM_MATRIX
        )
        print(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("SUCCESS: Exam generated!")
            print(f"Exam ID: {data.get('exam_id')}")
            print(f"Total questions: {data.get('tong_so_cau')}")
            print(f"DOCX file: {data.get('docx_filename')}")

            # Print sample questions
            questions = data.get("cau_hoi", [])
            if questions:
                print("\nSample questions:")
                for i, q in enumerate(questions[:2], 1):
                    print(f"\nQuestion {i}:")
                    print(f"  Type: {q.get('loai_cau')} - Level: {q.get('muc_do')}")
                    print(f"  Content: {q.get('noi_dung_cau_hoi')}")
                    print(f"  Answer: {q.get('dap_an')}")

            return True
        else:
            print(f"FAILED: {response.text}")
            return False

    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    print("=== Exam Generation Test ===")

    # Test connection
    if not test_connection():
        return

    # Test exam generation
    test_exam_generation()

    print("\n=== Test Complete ===")


if __name__ == "__main__":
    main()
