#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import asyncio
import json

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.exam_generation_service import ExamGenerationService
from app.models.exam_models import ExamMatrixRequest

async def debug_simple():
    """Debug the exam generation service directly"""
    
    print("=== DEBUGGING EXAM GENERATION SERVICE ===")
    
    # Create test data with correct structure
    test_data = {
        "lesson_id": "test_lesson",
        "mon_hoc": "Hoa hoc", 
        "lop": 12,
        "tong_so_cau": 1,
        "cau_hinh_de": [
            {
                "bai": "Bai 1: Cau tao nguyen tu",
                "so_cau": 1,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cau tao nguyen tu",
                        "yeu_cau_can_dat": "Hieu duoc cau tao nguyen tu",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 1,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    # Mock lesson content
    lesson_content = {
        "success": True,
        "content": {
            "main_content": "Nguyen tu la don vi co ban cua vat chat. Nguyen tu gom hat nhan mang dien tich duong va cac electron mang dien tich am quay quanh hat nhan.",
            "lesson_info": {
                "chapter_title": "Cau tao nguyen tu",
                "lesson_title": "Bai 1: Cau tao nguyen tu"
            }
        },
        "search_quality": 0.8
    }
    
    try:
        # Create service instance
        service = ExamGenerationService()
        
        # Create request object
        request = ExamMatrixRequest(**test_data)
        
        print("Calling generate_questions_from_matrix...")
        result = await service.generate_questions_from_matrix(request, lesson_content)
        
        print(f"Result success: {result.get('success', False)}")
        
        if result.get('success'):
            questions = result.get('questions', [])
            print(f"Number of questions: {len(questions)}")
            
            # Save to JSON file for analysis
            with open("debug_questions.json", "w", encoding="utf-8") as f:
                json.dump(questions, f, ensure_ascii=False, indent=2)
            
            print("Questions saved to debug_questions.json")
            
            # Analyze first question
            if questions:
                question = questions[0]
                print("\n=== FIRST QUESTION ANALYSIS ===")
                print(f"Type: {question.get('loai_cau', 'Unknown')}")
                
                # Check answer structure in detail
                dap_an = question.get("dap_an", {})
                print(f"Answer structure type: {type(dap_an)}")
                print(f"Answer structure keys: {list(dap_an.keys()) if isinstance(dap_an, dict) else 'Not a dict'}")
                
                # Save answer structure to separate file
                with open("debug_answer_structure.json", "w", encoding="utf-8") as f:
                    json.dump(dap_an, f, ensure_ascii=False, indent=2)
                
                print("Answer structure saved to debug_answer_structure.json")
                
                # Check if we have the correct answer
                if isinstance(dap_an, dict):
                    correct = dap_an.get("dung", "NOT_FOUND")
                    print(f"Correct answer from 'dung' key: '{correct}'")
                    
                    # Check all keys for potential answer
                    print("All answer keys and values:")
                    for key, value in dap_an.items():
                        print(f"  {key}: {value}")
                
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"Exception occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_simple())
